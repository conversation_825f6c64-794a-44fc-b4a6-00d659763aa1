"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_utils_clearUserCache_ts",{

/***/ "(app-pages-browser)/./src/utils/clearUserCache.ts":
/*!*************************************!*\
  !*** ./src/utils/clearUserCache.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearAllUserCache: () => (/* binding */ clearAllUserCache),\n/* harmony export */   clearUserSpecificCache: () => (/* binding */ clearUserSpecificCache)\n/* harmony export */ });\n// Utility to clear all user-related cache and storage\nasync function clearAllUserCache() {\n    console.log('Clearing all user cache and storage...');\n    try {\n        // Clear localStorage\n        if (true) {\n            localStorage.clear();\n            console.log('localStorage cleared');\n        }\n    } catch (error) {\n        console.warn('Failed to clear localStorage:', error);\n    }\n    try {\n        // Clear sessionStorage\n        if (true) {\n            sessionStorage.clear();\n            console.log('sessionStorage cleared');\n        }\n    } catch (error) {\n        console.warn('Failed to clear sessionStorage:', error);\n    }\n    try {\n        // Clear advanced cache\n        const { globalCache } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_utils_advancedCache_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/utils/advancedCache */ \"(app-pages-browser)/./src/utils/advancedCache.ts\"));\n        globalCache.clear();\n        console.log('Advanced cache cleared');\n    } catch (error) {\n        console.warn('Failed to clear advanced cache:', error);\n    }\n    try {\n        // Clear any browser cache for API requests\n        if ('caches' in window) {\n            const cacheNames = await caches.keys();\n            await Promise.all(cacheNames.map((cacheName)=>caches.delete(cacheName)));\n            console.log('Browser caches cleared');\n        }\n    } catch (error) {\n        console.warn('Failed to clear browser caches:', error);\n    }\n    try {\n        // Clear any IndexedDB data (if used)\n        if ('indexedDB' in window) {\n            // This is a more aggressive approach - you might want to be more selective\n            console.log('IndexedDB clearing not implemented (would need specific database names)');\n        }\n    } catch (error) {\n        console.warn('Failed to clear IndexedDB:', error);\n    }\n    console.log('Cache clearing completed');\n}\n// Clear user-specific cache entries only (less aggressive)\nasync function clearUserSpecificCache(userId) {\n    console.log('Clearing user-specific cache for user:', userId);\n    try {\n        // Clear advanced cache with user tags\n        const { globalCache } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_utils_advancedCache_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/utils/advancedCache */ \"(app-pages-browser)/./src/utils/advancedCache.ts\"));\n        globalCache.invalidateByTags([\n            'user',\n            'subscription',\n            'usage'\n        ]);\n        if (userId) {\n            globalCache.invalidateByTags([\n                \"user_\".concat(userId)\n            ]);\n        }\n        console.log('User-specific cache cleared');\n    } catch (error) {\n        console.warn('Failed to clear user-specific cache:', error);\n    }\n    try {\n        // Clear user-specific localStorage items\n        if (true) {\n            const keysToRemove = [];\n            for(let i = 0; i < localStorage.length; i++){\n                const key = localStorage.key(i);\n                if (key && (key.includes('user') || key.includes('subscription') || key.includes('auth'))) {\n                    keysToRemove.push(key);\n                }\n            }\n            keysToRemove.forEach((key)=>localStorage.removeItem(key));\n            console.log('User-specific localStorage items cleared:', keysToRemove);\n        }\n    } catch (error) {\n        console.warn('Failed to clear user-specific localStorage:', error);\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/clearUserCache.ts\n"));

/***/ })

});